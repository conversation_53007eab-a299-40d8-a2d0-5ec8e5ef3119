using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;
[ApiController]
[Route("/api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class AccountController : ControllerBase
{
    private readonly IAddressService _addressService;
    private readonly IOrderService _orderService;
    private readonly ICurrentUserService _currentUser;

    public AccountController(IAddressService addressService, IOrderService orderService, ICurrentUserService currentUser)
    {
        _addressService = addressService;
        _orderService = orderService;
        _currentUser = currentUser;
    }
    [HttpGet("{customerId}/addresses")]
    public async Task<List<AddressListDto>> GetAddressesAsync(Guid customerId)
    {
        try
        {
            return await _addressService.GetCustomerAddressesAsync(customerId);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return [];
        }
    }
    [HttpPost("{customerId}/addresses")]
    public async Task<Guid> CreateAddressAsync(Guid customerId, AddressCreateDto dto)
    {
        try
        {
            return await _addressService.CreateCustomerAddressAsync(customerId, dto);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return Guid.Empty;
        }
    }

    [HttpPut("addresses")]
    public async Task<bool> UpdateAddressAsync(AddressUpdateDto dto)
    {
        try
        {
            await _addressService.UpdateAsync(dto);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return false;
        }
    }
    [HttpDelete("addresses/{addressId}")]
    public async Task<bool> DeleteAddressAsync(Guid addressId)
    {
        try
        {
            await _addressService.DeleteAsync(addressId);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return false;
        }
    }

    [HttpGet("orders")]
    public async Task<ActionResult<ApiResponse<List<OrderListDto>>>> GetOrdersAsync()
    {
        try
        {
            var userId = _currentUser.UserId;
            if (userId == null)
                return Unauthorized(ApiResponse<List<OrderListDto>>.ErrorResponse("Oturum bulunamadı.", 401));

            var orders = await _orderService.GetOrdersByCustomerAsync(userId.Value);
            return Ok(ApiResponse<List<OrderListDto>>.SuccessResponse(orders, "Siparişler başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<OrderListDto>>.ErrorResponse("Siparişler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}
