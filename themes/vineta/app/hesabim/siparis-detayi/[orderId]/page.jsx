import { server<PERSON>pi } from '@/lib/api/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { redirect } from 'next/navigation';
import Breadcumb from "@/components/common/Breadcumb";
import Footer1 from "@/components/footers/Footer1";
import Header1 from "@/components/headers/Header1";
import Topbar2 from "@/components/headers/Topbar2";
import Sidebar from "@/components/dashboard/Sidebar";
import Link from "next/link";
import Image from "next/image";

// Server-side data fetching
async function getOrderDetail(orderId) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      redirect('/login');
    }

    const orderData = await serverApi.get(`/orders/${orderId}`);
    return orderData.data || orderData;
  } catch (error) {
    console.error('Error fetching order details:', error);
    return null;
  }
}

// Utility functions
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
};

const getStatusText = (status) => {
  const statusMap = {
    'Pending': 'Beklemede',
    'Processing': 'İşleniyor',
    'Shipped': 'Kargoda',
    'Delivered': 'Teslim Edildi',
    'Cancelled': 'İptal Edildi',
    'Returned': 'İade Edildi'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status) => {
  const statusClassMap = {
    'Pending': 'text-warning',
    'Processing': 'text-info',
    'Shipped': 'text-on-the-way',
    'Delivered': 'text-delivered',
    'Cancelled': 'text-danger',
    'Returned': 'text-secondary'
  };
  return statusClassMap[status] || 'text-muted';
};

export const metadata = {
  title: "Sipariş Detayı || Vineta - Multipurpose React Nextjs eCommerce",
  description: "Vineta - Multipurpose React Nextjs eCommerce",
};

export default async function OrderDetailPage({ params }) {
  const { orderId } = params;
  const orderDetail = await getOrderDetail(orderId);

  if (!orderDetail) {
    return (
      <>
        <Breadcumb pageName="Sipariş Detayı" pageTitle="Sipariş Bulunamadı" />
        <div className="flat-spacing-13">
          <div className="container">
            <div className="text-center">
              <h3>Sipariş Bulunamadı</h3>
              <p>Aradığınız sipariş bulunamadı veya erişim yetkiniz bulunmuyor.</p>
              <Link href="/hesabim/siparislerim" className="tf-btn animate-btn bg-dark-2">
                Siparişlerime Dön
              </Link>
            </div>
          </div>
        </div>
        <Footer1 />
      </>
    );
  }

  return (
    <>
      <Breadcumb
        pageName="Sipariş Detayı"
        pageTitle={`Sipariş #${orderDetail.orderNumber}`}
      />

      <div className="flat-spacing-13">
        <div className="container-7">
          {/* sidebar-account */}
          <div className="btn-sidebar-mb d-lg-none">
            <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
              <i className="icon icon-sidebar" />
            </button>
          </div>
          {/* /sidebar-account */}

          <div className="main-content-account">
            <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
              <ul className="my-account-nav">
                <Sidebar />
              </ul>
            </div>

            <div className="my-acount-content account-orders">
              <div className="account-orders-wrap">
                {/* Modal Style Header */}
                <div className="header mb-4">
                  <div className="heading">Sipariş Detayı</div>
                  <Link
                    href="/hesabim/siparislerim"
                    className="tf-btn animate-btn btn-outline-dark"
                  >
                    ← Siparişlerime Dön
                  </Link>
                </div>

                <div
                  className="modal fade modalCentered modal-order-detail"
                  id="order_detail"
                >
                  <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                      <div className="header">
                        <div className="heading">Sipariş Detayı</div>
                        <span
                          className="icon-close icon-close-popup"
                          data-bs-dismiss="modal"
                        />
                      </div>
                      {loading ? (
                        <div className="text-center py-4">
                          <div className="spinner-border" role="status">
                            <span className="visually-hidden">Yükleniyor...</span>
                          </div>
                          <div className="mt-2">Sipariş detayları yükleniyor...</div>
                        </div>
                      ) : error ? (
                        <div className="alert alert-danger mx-3" role="alert">
                          <strong>Hata:</strong> {error}
                        </div>
                      ) : orderDetail ? (
                        <>
                          <ul className="list-infor">
                            <li>#{orderDetail.orderNumber}</li>
                            <li>{formatDate(orderDetail.createdAt)}</li>
                            <li>{orderDetail.orderRows?.length || 0} ürün</li>
                            <li className={getStatusClass(orderDetail.status)}>
                              {getStatusText(orderDetail.status)}
                            </li>
                          </ul>
                          <div className="tb-order-detail">
                            <div className="top">
                              <div className="title item">Ürün</div>
                              <div className="title item d-md-block d-none">Adet</div>
                              <div className="title item d-md-block d-none">Fiyat</div>
                              <div className="title item d-md-block d-none">Toplam</div>
                            </div>
                            <div className="tb-content">
                              {orderDetail.orderRows?.map((orderRow, i) => (
                                <div key={i} className="order-detail-item">
                                  <div className="item">
                                    <div className="infor-content">
                                      <div className="image">
                                        <Link href={`/urunler/${orderRow.product?.slug || orderRow.productId}`}>
                                          <Image
                                            className="lazyload"
                                            alt="img-product"
                                            src={orderRow.product?.images?.[0]?.thumbnailMediumPath || '/images/products/default.jpg'}
                                            width={684}
                                            height={972}
                                          />
                                        </Link>
                                      </div>
                                      <div>
                                        <Link
                                          className="link"
                                          href={`/urunler/${orderRow.product?.slug || orderRow.productId}`}
                                        >
                                          {orderRow.product?.name || 'Ürün Adı'}
                                        </Link>
                                        <div className="size">
                                          {orderRow.product?.sku && `SKU: ${orderRow.product.sku}`}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="item" data-title="Adet">
                                    {orderRow.quantity}
                                  </div>
                                  <div className="item" data-title="Fiyat">
                                    ₺{orderRow.discountedPrice.toFixed(2)}
                                  </div>
                                  <div className="item" data-title="Toplam">
                                    ₺{(orderRow.discountedPrice * orderRow.quantity).toFixed(2)}
                                  </div>
                                </div>
                              ))}
                              <div className="order-detail-item subtotal">
                                <div className="item d-md-block d-none" />
                                <div className="item d-md-block d-none" />
                                <div className="item subtotal-text">Ara Toplam:</div>
                                <div className="item subtotal-price">
                                  ₺{orderDetail.totalAmount.toFixed(2)}
                                </div>
                              </div>
                              {orderDetail.discountAmount > 0 && (
                                <div className="order-detail-item subtotal">
                                  <div className="item d-md-block d-none" />
                                  <div className="item d-md-block d-none" />
                                  <div className="item subtotal-text">İndirim:</div>
                                  <div className="item subtotal-price text-success">
                                    -₺{orderDetail.discountAmount.toFixed(2)}
                                  </div>
                                </div>
                              )}
                              {orderDetail.shippingAmount > 0 && (
                                <div className="order-detail-item subtotal">
                                  <div className="item d-md-block d-none" />
                                  <div className="item d-md-block d-none" />
                                  <div className="item subtotal-text">Kargo:</div>
                                  <div className="item subtotal-price">
                                    ₺{orderDetail.shippingAmount.toFixed(2)}
                                  </div>
                                </div>
                              )}
                              <div className="order-detail-item subtotal">
                                <div className="item d-md-block d-none" />
                                <div className="item d-md-block d-none" />
                                <div className="item subtotal-text"><strong>Toplam:</strong></div>
                                <div className="item subtotal-price">
                                  <strong>₺{orderDetail.totalAmount.toFixed(2)}</strong>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="bottom text-center">
                            Siparişinizden memnun değil misiniz?
                            <span className="fw-medium"> 14 gün</span> içinde {" "}
                            <Link
                              href={`/iade-ve-degisim`}
                              className="fw-medium btn-underline"
                            >
                              Ücretsiz iade talebinde bulunabilirsiniz
                            </Link>
                          </div>
                        </>
                      ) : (
                        <div className="text-center py-4">
                          <p>Sipariş detayları bulunamadı.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer1 />
    </>
  );
}
