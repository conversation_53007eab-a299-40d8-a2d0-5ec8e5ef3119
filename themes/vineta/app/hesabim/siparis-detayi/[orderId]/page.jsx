import { server<PERSON>pi } from '@/lib/api/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { redirect } from 'next/navigation';
import Breadcumb from "@/components/common/Breadcumb";
import Footer1 from "@/components/footers/Footer1";
import Header1 from "@/components/headers/Header1";
import Topbar2 from "@/components/headers/Topbar2";
import Sidebar from "@/components/dashboard/Sidebar";
import Link from "next/link";
import Image from "next/image";

// Server-side data fetching
async function getOrderDetail(orderId) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      redirect('/login');
    }

    const orderData = await serverApi.get(`/orders/${orderId}`);
    return orderData.data || orderData;
  } catch (error) {
    console.error('Error fetching order details:', error);
    return null;
  }
}

// Utility functions
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
};

const getStatusText = (status) => {
  const statusMap = {
    'Pending': 'Beklemede',
    'Processing': 'İşleniyor',
    'Shipped': 'Kargoda',
    'Delivered': 'Teslim Edildi',
    'Cancelled': 'İptal Edildi',
    'Returned': 'İade Edildi'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status) => {
  const statusClassMap = {
    'Pending': 'text-warning',
    'Processing': 'text-info',
    'Shipped': 'text-on-the-way',
    'Delivered': 'text-delivered',
    'Cancelled': 'text-danger',
    'Returned': 'text-secondary'
  };
  return statusClassMap[status] || 'text-muted';
};

export const metadata = {
  title: "Sipariş Detayı || Vineta - Multipurpose React Nextjs eCommerce",
  description: "Vineta - Multipurpose React Nextjs eCommerce",
};

export default async function OrderDetailPage({ params }) {
  const { orderId } = params;
  const orderDetail = await getOrderDetail(orderId);

  if (!orderDetail) {
    return (
      <>
        <Topbar2 />
        <Header1 />
        <Breadcumb pageName="Sipariş Detayı" pageTitle="Sipariş Bulunamadı" />
        <div className="flat-spacing-13">
          <div className="container">
            <div className="text-center">
              <h3>Sipariş Bulunamadı</h3>
              <p>Aradığınız sipariş bulunamadı veya erişim yetkiniz bulunmuyor.</p>
              <Link href="/hesabim/siparislerim" className="tf-btn animate-btn bg-dark-2">
                Siparişlerime Dön
              </Link>
            </div>
          </div>
        </div>
        <Footer1 />
      </>
    );
  }

  return (
    <>
      <Topbar2 />
      <Header1 />
      <Breadcumb 
        pageName="Sipariş Detayı" 
        pageTitle={`Sipariş #${orderDetail.orderNumber}`} 
      />
      
      <div className="flat-spacing-13">
        <div className="container-7">
          {/* sidebar-account */}
          <div className="btn-sidebar-mb d-lg-none">
            <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
              <i className="icon icon-sidebar" />
            </button>
          </div>
          {/* /sidebar-account */}

          <div className="main-content-account">
            <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
              <ul className="my-account-nav">
                <Sidebar />
              </ul>
            </div>
            
            <div className="my-acount-content account-orders">
              {/* Order Header */}
              <div className="order-header mb-4">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h4 className="mb-0">Sipariş Detayı</h4>
                  <Link 
                    href="/hesabim/siparislerim" 
                    className="tf-btn animate-btn btn-outline-dark"
                  >
                    ← Siparişlerime Dön
                  </Link>
                </div>
                
                <div className="order-info-cards row">
                  <div className="col-md-3 col-6 mb-3">
                    <div className="card h-100">
                      <div className="card-body text-center">
                        <h6 className="card-title">Sipariş No</h6>
                        <p className="card-text fw-bold">#{orderDetail.orderNumber}</p>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-3 col-6 mb-3">
                    <div className="card h-100">
                      <div className="card-body text-center">
                        <h6 className="card-title">Tarih</h6>
                        <p className="card-text">{formatDate(orderDetail.createdAt)}</p>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-3 col-6 mb-3">
                    <div className="card h-100">
                      <div className="card-body text-center">
                        <h6 className="card-title">Durum</h6>
                        <p className={`card-text fw-bold ${getStatusClass(orderDetail.status)}`}>
                          {getStatusText(orderDetail.status)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-3 col-6 mb-3">
                    <div className="card h-100">
                      <div className="card-body text-center">
                        <h6 className="card-title">Toplam</h6>
                        <p className="card-text fw-bold">₺{orderDetail.totalAmount.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="order-items mb-4">
                <h5 className="mb-3">Sipariş Ürünleri</h5>
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Ürün</th>
                        <th className="text-center">Adet</th>
                        <th className="text-end">Birim Fiyat</th>
                        <th className="text-end">Toplam</th>
                      </tr>
                    </thead>
                    <tbody>
                      {orderDetail.orderRows?.map((orderRow, index) => (
                        <tr key={index}>
                          <td>
                            <div className="d-flex align-items-center">
                              <div className="product-image me-3">
                                <Image
                                  src={orderRow.product?.images?.[0]?.thumbnailMediumPath || '/images/products/default.jpg'}
                                  alt={orderRow.product?.name || 'Ürün'}
                                  width={60}
                                  height={60}
                                  className="rounded"
                                />
                              </div>
                              <div>
                                <h6 className="mb-1">
                                  <Link 
                                    href={`/urunler/${orderRow.product?.slug || orderRow.productId}`}
                                    className="text-decoration-none"
                                  >
                                    {orderRow.product?.name || 'Ürün Adı'}
                                  </Link>
                                </h6>
                                {orderRow.product?.sku && (
                                  <small className="text-muted">SKU: {orderRow.product.sku}</small>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="text-center">{orderRow.quantity}</td>
                          <td className="text-end">₺{orderRow.discountedPrice.toFixed(2)}</td>
                          <td className="text-end fw-bold">
                            ₺{(orderRow.discountedPrice * orderRow.quantity).toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Order Summary */}
              <div className="order-summary">
                <div className="row">
                  <div className="col-md-6 offset-md-6">
                    <div className="card">
                      <div className="card-header">
                        <h6 className="mb-0">Sipariş Özeti</h6>
                      </div>
                      <div className="card-body">
                        <div className="d-flex justify-content-between mb-2">
                          <span>Ara Toplam:</span>
                          <span>₺{orderDetail.grossTotalAmount?.toFixed(2) || orderDetail.totalAmount.toFixed(2)}</span>
                        </div>
                        {orderDetail.discountAmount > 0 && (
                          <div className="d-flex justify-content-between mb-2 text-success">
                            <span>İndirim:</span>
                            <span>-₺{orderDetail.discountAmount.toFixed(2)}</span>
                          </div>
                        )}
                        {orderDetail.shippingAmount > 0 && (
                          <div className="d-flex justify-content-between mb-2">
                            <span>Kargo:</span>
                            <span>₺{orderDetail.shippingAmount.toFixed(2)}</span>
                          </div>
                        )}
                        {orderDetail.taxAmount > 0 && (
                          <div className="d-flex justify-content-between mb-2">
                            <span>Vergi:</span>
                            <span>₺{orderDetail.taxAmount.toFixed(2)}</span>
                          </div>
                        )}
                        <hr />
                        <div className="d-flex justify-content-between fw-bold">
                          <span>Toplam:</span>
                          <span>₺{orderDetail.totalAmount.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="additional-info mt-4">
                <div className="row">
                  {orderDetail.address && (
                    <div className="col-md-6 mb-3">
                      <div className="card">
                        <div className="card-header">
                          <h6 className="mb-0">Teslimat Adresi</h6>
                        </div>
                        <div className="card-body">
                          <p className="mb-1 fw-bold">{orderDetail.address.name}</p>
                          <p className="mb-1">{orderDetail.address.line1}</p>
                          {orderDetail.address.line2 && (
                            <p className="mb-1">{orderDetail.address.line2}</p>
                          )}
                          <p className="mb-0">
                            {orderDetail.address.district}, {orderDetail.address.city}
                          </p>
                          {orderDetail.address.postalCode && (
                            <p className="mb-0">{orderDetail.address.postalCode}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {orderDetail.notes && (
                    <div className="col-md-6 mb-3">
                      <div className="card">
                        <div className="card-header">
                          <h6 className="mb-0">Sipariş Notları</h6>
                        </div>
                        <div className="card-body">
                          <p className="mb-0">{orderDetail.notes}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Return Policy */}
              <div className="return-policy mt-4 text-center">
                <p className="text-muted">
                  Siparişinizden memnun değil misiniz?{" "}
                  <Link href="/iade-ve-degisim" className="fw-medium text-decoration-none">
                    Ücretsiz iade talebinde bulunabilirsiniz
                  </Link>
                  {" "}14 gün içinde
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer1 />
    </>
  );
}
