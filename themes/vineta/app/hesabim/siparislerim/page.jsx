import Breadcumb from "@/components/common/Breadcumb";
import Orders from "@/components/dashboard/Orders";
import React from "react";

/**
 * @typedef {Object} OrderItem
 * @property {number} productId - Unique product identifier
 * @property {string} title - Product title
 * @property {string} image - Product image URL
 * @property {number} quantity - Number of items ordered
 * @property {number} price - Unit price
 * @property {number} totalPrice - Total price (quantity * price)
 * @property {Variant} variant - Product variant details
 */

/**
 * @typedef {Object} OrdersPage
 * @property {PageMetadata} metadata - Page metadata
 * @property {Order[]} orders - List of orders
 */

/**
 * @typedef {Order & {
 *   items: OrderItem[],
 *   subtotal: number,
 * }} OrderDetail
 */

/* 
const orderRequest = {
  // Sepetteki ürünleri getir
  getOrders: () => api.get('account/orders')
}; 
*/




export const metadata = {
  title: "Siparişlerim || Future Cosmetics",
  description: "Geçmiş siparişlerinizi görüntüleyin.",
  robots: "noindex, nofollow", // Sipariş sayfaları indexlenmemeli
};
export default function page() {
  return (
    <>
      <Breadcumb pageName="Siparişlerim" pageTitle="Siparişlerim" />
      <Orders />
    </>
  );
}
