'use client';
import Image from "next/image";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { accountRequests } from "@/services/account";

export default function OrderDetails() {
  const [orderDetail, setOrderDetail] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleModalShow = async () => {
      if (window.selectedOrder) {
        try {
          setLoading(true);
          setError(null);
          const orderData = await accountRequests.getOrderById(window.selectedOrder.id);
          setOrderDetail(orderData);
        } catch (err) {
          console.error('Error fetching order details:', err);
          setError(err.message || 'Sipariş detayları yüklenirken bir hata oluştu');
        } finally {
          setLoading(false);
        }
      }
    };

    // Modal açıldığında çalışacak event listener
    const modalElement = document.getElementById('order_detail');
    if (modalElement) {
      modalElement.addEventListener('show.bs.modal', handleModalShow);
      return () => {
        modalElement.removeEventListener('show.bs.modal', handleModalShow);
      };
    }
  }, []);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  };

  const getStatusText = (status) => {
    const statusMap = {
      'Pending': 'Beklemede',
      'Processing': 'İşleniyor',
      'Shipped': 'Kargoda',
      'Delivered': 'Teslim Edildi',
      'Cancelled': 'İptal Edildi',
      'Returned': 'İade Edildi'
    };
    return statusMap[status] || status;
  };

  const getStatusClass = (status) => {
    const statusClassMap = {
      'Pending': 'text-warning',
      'Processing': 'text-info',
      'Shipped': 'text-on-the-way',
      'Delivered': 'text-delivered',
      'Cancelled': 'text-danger',
      'Returned': 'text-secondary'
    };
    return statusClassMap[status] || 'text-muted';
  };
  return (
    <div
      className="modal fade modalCentered modal-order-detail"
      id="order_detail"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="header">
            <div className="heading">Sipariş Detayı</div>
            <span
              className="icon-close icon-close-popup"
              data-bs-dismiss="modal"
            />
          </div>
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Yükleniyor...</span>
              </div>
              <div className="mt-2">Sipariş detayları yükleniyor...</div>
            </div>
          ) : error ? (
            <div className="alert alert-danger mx-3" role="alert">
              <strong>Hata:</strong> {error}
            </div>
          ) : orderDetail ? (
            <>
              <ul className="list-infor">
                <li>#{orderDetail.orderNumber}</li>
                <li>{formatDate(orderDetail.createdAt)}</li>
                <li>{orderDetail.orderRows?.length || 0} ürün</li>
                <li className={getStatusClass(orderDetail.status)}>
                  {getStatusText(orderDetail.status)}
                </li>
              </ul>
              <div className="tb-order-detail">
                <div className="top">
                  <div className="title item">Ürün</div>
                  <div className="title item d-md-block d-none">Adet</div>
                  <div className="title item d-md-block d-none">Fiyat</div>
                  <div className="title item d-md-block d-none">Toplam</div>
                </div>
                <div className="tb-content">
                  {orderDetail.orderRows?.map((orderRow, i) => (
                    <div key={i} className="order-detail-item">
                      <div className="item">
                        <div className="infor-content">
                          <div className="image">
                            <Link href={`/urunler/${orderRow.product?.slug || orderRow.productId}`}>
                              <Image
                                className="lazyload"
                                alt="img-product"
                                src={orderRow.product?.images?.[0]?.thumbnailMediumPath || '/images/products/default.jpg'}
                                width={684}
                                height={972}
                              />
                            </Link>
                          </div>
                          <div>
                            <Link
                              className="link"
                              href={`/urunler/${orderRow.product?.slug || orderRow.productId}`}
                            >
                              {orderRow.product?.name || 'Ürün Adı'}
                            </Link>
                            <div className="size">
                              {orderRow.product?.sku && `SKU: ${orderRow.product.sku}`}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="item" data-title="Adet">
                        {orderRow.quantity}
                      </div>
                      <div className="item" data-title="Fiyat">
                        ₺{orderRow.discountedPrice.toFixed(2)}
                      </div>
                      <div className="item" data-title="Toplam">
                        ₺{(orderRow.discountedPrice * orderRow.quantity).toFixed(2)}
                      </div>
                    </div>
                  ))}
                  <div className="order-detail-item subtotal">
                    <div className="item d-md-block d-none" />
                    <div className="item d-md-block d-none" />
                    <div className="item subtotal-text">Ara Toplam:</div>
                    <div className="item subtotal-price">
                      ₺{orderDetail.totalAmount.toFixed(2)}
                    </div>
                  </div>
                  {orderDetail.discountAmount > 0 && (
                    <div className="order-detail-item subtotal">
                      <div className="item d-md-block d-none" />
                      <div className="item d-md-block d-none" />
                      <div className="item subtotal-text">İndirim:</div>
                      <div className="item subtotal-price text-success">
                        -₺{orderDetail.discountAmount.toFixed(2)}
                      </div>
                    </div>
                  )}
                  {orderDetail.shippingAmount > 0 && (
                    <div className="order-detail-item subtotal">
                      <div className="item d-md-block d-none" />
                      <div className="item d-md-block d-none" />
                      <div className="item subtotal-text">Kargo:</div>
                      <div className="item subtotal-price">
                        ₺{orderDetail.shippingAmount.toFixed(2)}
                      </div>
                    </div>
                  )}
                  <div className="order-detail-item subtotal">
                    <div className="item d-md-block d-none" />
                    <div className="item d-md-block d-none" />
                    <div className="item subtotal-text"><strong>Toplam:</strong></div>
                    <div className="item subtotal-price">
                      <strong>₺{orderDetail.totalAmount.toFixed(2)}</strong>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bottom text-center">
                Siparişinizden memnun değil misiniz?
                <Link
                  <span className="fw-medium"> 14 gün</span> içinde
                  href={`/iade-ve-degisim`}
                  className="fw-medium btn-underline"
                >
                  Ücretsiz iade talebinde bulunabilirsiniz
                </Link>
              </div>
            </>
          ) : (
            <div className="text-center py-4">
              <p>Sipariş detayları bulunamadı.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
